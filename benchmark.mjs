#!/usr/bin/env node

import { spawn } from 'child_process';
import { performance } from 'perf_hooks';

function runCommand(command, args = []) {
    return new Promise((resolve, reject) => {
        const startTime = performance.now();
        console.log(`\n🚀 Running: ${command} ${args.join(' ')}`);
        
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true
        });
        
        child.on('close', (code) => {
            const endTime = performance.now();
            const duration = ((endTime - startTime) / 1000).toFixed(2);
            
            if (code === 0) {
                console.log(`✅ Completed in ${duration}s`);
                resolve({ duration: parseFloat(duration), success: true });
            } else {
                console.log(`❌ Failed with code ${code} after ${duration}s`);
                resolve({ duration: parseFloat(duration), success: false });
            }
        });
        
        child.on('error', (error) => {
            console.error(`❌ Error: ${error.message}`);
            reject(error);
        });
    });
}

async function benchmark() {
    console.log('🏁 Phoenix Build System Benchmark');
    console.log('==================================');
    
    const results = {};
    
    // Test 1: Current Gulp system
    console.log('\n📊 Test 1: Current Gulp System');
    console.log('-------------------------------');
    try {
        const gulpResult = await runCommand('npx', ['gulp', 'dev']);
        results.gulp = gulpResult;
    } catch (error) {
        console.error('Gulp test failed:', error);
        results.gulp = { duration: 0, success: false };
    }
    
    // Test 2: New esbuild system
    console.log('\n📊 Test 2: New esbuild System');
    console.log('------------------------------');
    try {
        const esbuildResult = await runCommand('node', ['build.mjs']);
        results.esbuild = esbuildResult;
    } catch (error) {
        console.error('esbuild test failed:', error);
        results.esbuild = { duration: 0, success: false };
    }
    
    // Test 3: esbuild with parallel processing
    console.log('\n📊 Test 3: esbuild with Parallel Processing');
    console.log('--------------------------------------------');
    try {
        const esbuildParallelResult = await runCommand('node', ['build.mjs', '--parallel']);
        results.esbuildParallel = esbuildParallelResult;
    } catch (error) {
        console.error('esbuild parallel test failed:', error);
        results.esbuildParallel = { duration: 0, success: false };
    }
    
    // Results summary
    console.log('\n🏆 BENCHMARK RESULTS');
    console.log('====================');
    
    if (results.gulp.success) {
        console.log(`Gulp (current):           ${results.gulp.duration}s`);
    } else {
        console.log(`Gulp (current):           FAILED`);
    }
    
    if (results.esbuild.success) {
        console.log(`esbuild:                  ${results.esbuild.duration}s`);
        if (results.gulp.success) {
            const improvement = ((results.gulp.duration - results.esbuild.duration) / results.gulp.duration * 100).toFixed(1);
            console.log(`                          ${improvement}% faster than Gulp`);
        }
    } else {
        console.log(`esbuild:                  FAILED`);
    }
    
    if (results.esbuildParallel.success) {
        console.log(`esbuild (parallel):       ${results.esbuildParallel.duration}s`);
        if (results.gulp.success) {
            const improvement = ((results.gulp.duration - results.esbuildParallel.duration) / results.gulp.duration * 100).toFixed(1);
            console.log(`                          ${improvement}% faster than Gulp`);
        }
        if (results.esbuild.success) {
            const improvement = ((results.esbuild.duration - results.esbuildParallel.duration) / results.esbuild.duration * 100).toFixed(1);
            console.log(`                          ${improvement}% faster than esbuild`);
        }
    } else {
        console.log(`esbuild (parallel):       FAILED`);
    }
    
    // Find the fastest
    const successful = Object.entries(results).filter(([_, result]) => result.success);
    if (successful.length > 0) {
        const fastest = successful.reduce((min, [name, result]) => 
            result.duration < min.duration ? { name, ...result } : min
        );
        console.log(`\n🥇 Fastest: ${fastest.name} (${fastest.duration}s)`);
    }
}

benchmark().catch(console.error);
