<?php
add_action('after_setup_theme', 'remove_default_patterns');
function remove_default_patterns() {
	remove_theme_support('core-block-patterns');
}

add_filter('allowed_block_types_all', 'filter_gutenberg_blocks', 25, 2);
function filter_gutenberg_blocks($allowedBlocks, $editorContext) {
	$registeredBlocks = array_keys(WP_Block_Type_Registry::get_instance()->get_all_registered());
	$allowedBlocks = [];

	define('ACF_BLOCK_SLUG', 'acf/');

	if (
		$editorContext->post->post_type == START_PAGE_SLUG ||
		$editorContext->post->post_type == DYNAMIC_SLUG ||
		$editorContext->post->post_type == SEO_PAGE_SLUG ||
		str_contains(get_page_template(), 'page-dynamic-blocks.php')
	) {
		// Allow Phoenix blocks and dynamic blocks
		foreach ($registeredBlocks as $registeredBlock) {
			if (
				str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_BLOCK_SLUG) ||
				str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_PNP_BLOCK_SLUG)
			) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	if (
		$editorContext->post->post_type == RESPONSIBLE_GAMING_SLUG ||
		str_contains(get_page_template(), 'page-responsible-gaming.php')
	) {
		// Allow Phoenix blocks and responsible gaming blocks
		foreach ($registeredBlocks as $registeredBlock) {
			if (str_contains($registeredBlock, ACF_BLOCK_SLUG . RESPONSIBLE_GAMING_BLOCK_SLUG)) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	if ($editorContext->post->post_type == AD_BANNER_SLUG) {
		foreach ($registeredBlocks as $registeredBlock) {
			if (str_contains($registeredBlock, ACF_BLOCK_SLUG . AD_BANNER_BLOCK_SLUG)) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	// For all other contexts, allow Phoenix blocks and non-dynamic blocks
	foreach ($registeredBlocks as $registeredBlock) {
		if (
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_BLOCK_SLUG) &&
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_PNP_BLOCK_SLUG) &&
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . RESPONSIBLE_GAMING_BLOCK_SLUG)
		) {
			$allowedBlocks[] = $registeredBlock;
		}
	}

	return $allowedBlocks;
}

add_filter('admin_body_class', 'add_pnp_body_classes'); // this should be removed after refactoring the pnp blocks
function add_pnp_body_classes($classes) {
	return "{$classes} pnp";
}
