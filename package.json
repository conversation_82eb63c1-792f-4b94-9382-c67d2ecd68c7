{"name": "phoenix-nodejs", "version": "2.9.3", "description": "Generating assets for all themes using gulp", "main": "gulpfile.mjs", "scripts": {"start": "gulp dev", "publish": "gulp"}, "author": "Phoenix Team - ComeOn!", "license": "ISC", "repository": {"type": "git", "url": "https://bitbucket.org/comeonwordpress/phoenix/", "directory": "/"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.26.10", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.10", "@eslint/js": "^9.33.0", "@ronilaukkarinen/gulp-stylelint": "14.1.2", "chokidar": "^4.0.1", "del": "^7.1.0", "esbuild": "^0.20.2", "eslint": "^9.33.0", "glob": "^10.4.5", "gulp": "^5.0.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-dart-sass": "^1.1.0", "gulp-eslint": "^6.0.0", "gulp-filter": "^9.0.1", "gulp-sass-glob": "^1.1.0", "gulp-size": "^5.0.0", "gulp-touch-cmd": "^0.0.1", "gulp-uglify": "^3.0.2", "gulplog": "^2.2.0", "node-fetch": "^3.3.2", "sass": "1.77.6", "sharp": "^0.33.5", "stylelint": "15.11.0", "stylelint-config-standard-scss": "11.1.0", "stylelint-order": "6.0.3", "stylelint-scss": "5.3.1"}}