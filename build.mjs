#!/usr/bin/env node

import * as esbuild from 'esbuild';
import { promises as fs } from 'fs';
import path from 'path';
import sharp from 'sharp';
import { spawn } from 'child_process';

// Configuration
const themes = [
    '888', 'casinostugan', 'casinostuen', 'cherrycasino', 'comeon', 'euroslots',
    'folkeriket', 'galaksino', 'getlucky', 'hajper', 'lyllo', 'mobilautomaten',
    'mobilebet', 'nopeampi', 'norgesspill', 'phoenix', 'pzbuk', 'snabbare',
    'spinon', 'sunmaker', 'suomikasino', 'wespin'
];

const isDev = process.argv.includes('--dev');
const isParallel = process.argv.includes('--parallel');

// Utility functions
const log = (theme, message) => {
    const timestamp = new Date().toISOString().substring(11, 19);
    console.log(`[${timestamp}] ${theme.toUpperCase()}: ${message}`);
};

const logGlobal = (message) => {
    const timestamp = new Date().toISOString().substring(11, 19);
    console.log(`[${timestamp}] 🚀 ${message}`);
};

// Clean dist directories
async function cleanDist() {
    logGlobal('Cleaning dist directories...');
    const cleanPromises = themes.map(async (theme) => {
        try {
            await fs.rm(`${theme}/dist`, { recursive: true, force: true });
            await fs.mkdir(`${theme}/dist`, { recursive: true });
        } catch (error) {
            // Directory might not exist, that's ok
        }
    });
    await Promise.all(cleanPromises);
    logGlobal('✅ Clean completed');
}

// Build JavaScript with esbuild (extremely fast)
async function buildJavaScript() {
    logGlobal('🔧 Building JavaScript with esbuild...');
    const startTime = Date.now();

    try {
        // Main script bundle
        await esbuild.build({
            entryPoints: ['phoenix/app/scripts/main.js'],
            bundle: true,
            minify: !isDev,
            sourcemap: isDev,
            target: ['es2020'],
            outfile: 'phoenix/dist/main.js',
            format: 'iife',
            platform: 'browser',
            define: {
                'process.env.NODE_ENV': isDev ? '"development"' : '"production"'
            }
        });

        // Admin global scripts
        await esbuild.build({
            entryPoints: ['phoenix/admin/scripts/global/admin-global.js'],
            bundle: true,
            minify: !isDev,
            sourcemap: isDev,
            target: ['es2020'],
            outfile: 'phoenix/dist/admin.min.js',
            format: 'iife',
            platform: 'browser'
        });

        // Admin post scripts - just try to build one main file
        try {
            await fs.access('phoenix/admin/scripts/post/brackets.js');
            await esbuild.build({
                entryPoints: ['phoenix/admin/scripts/post/brackets.js'],
                bundle: true,
                minify: !isDev,
                sourcemap: isDev,
                target: ['es2020'],
                outfile: 'phoenix/dist/admin-post.min.js',
                format: 'iife',
                platform: 'browser'
            });
        } catch (error) {
            // No post scripts found, create empty file
            await fs.writeFile('phoenix/dist/admin-post.min.js', '// No admin post scripts found\n');
        }

        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        logGlobal(`✅ JavaScript built in ${duration}s`);
    } catch (error) {
        console.error('❌ JavaScript build failed:', error);
        throw error;
    }
}

// Build SCSS with esbuild (much faster than Dart Sass)
async function buildSCSS(theme) {
    const startTime = Date.now();

    const scssFiles = [
        'page.scss',
        'campaign.scss',
        'blog.scss',
        'games.scss',
        'help-page.scss',
        'survey.scss',
        'responsible-gaming.scss',
        'ad-banner.scss'
    ];

    const buildPromises = scssFiles.map(async (file) => {
        const inputPath = `${theme}/app/styles/${file}`;
        const outputPath = `${theme}/dist/${file.replace('.scss', '.css')}`;

        try {
            // Check if file exists
            await fs.access(inputPath);

            await esbuild.build({
                entryPoints: [inputPath],
                bundle: true,
                minify: !isDev,
                sourcemap: isDev,
                outfile: outputPath,
                loader: {
                    '.scss': 'css',
                    '.sass': 'css'
                },
                resolveExtensions: ['.scss', '.sass', '.css'],
                external: []
            });

            log(theme, `✅ ${file} compiled`);
        } catch (error) {
            if (error.code !== 'ENOENT') {
                log(theme, `❌ Failed to compile ${file}: ${error.message}`);
            }
        }
    });

    await Promise.all(buildPromises);

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    log(theme, `✅ All SCSS compiled in ${duration}s`);
}

// Build admin SCSS
async function buildAdminSCSS(theme) {
    try {
        // For phoenix theme, use the main admin styles
        let inputPath, outputPath;

        if (theme === 'phoenix') {
            inputPath = `${theme}/admin/styles/main.scss`;
            outputPath = `${theme}/dist/admin.min.css`;
        } else {
            // For other themes, check if they have their own admin styles
            inputPath = `${theme}/admin/styles/admin.scss`;
            outputPath = `${theme}/dist/admin.min.css`;

            // If theme doesn't have admin styles, try main.scss
            try {
                await fs.access(inputPath);
            } catch (error) {
                inputPath = `${theme}/admin/styles/main.scss`;
            }
        }

        await fs.access(inputPath);

        await esbuild.build({
            entryPoints: [inputPath],
            bundle: true,
            minify: !isDev,
            sourcemap: isDev,
            outfile: outputPath,
            loader: {
                '.scss': 'css',
                '.sass': 'css'
            }
        });

        log(theme, '✅ Admin SCSS compiled');
    } catch (error) {
        if (error.code !== 'ENOENT') {
            log(theme, `❌ Admin SCSS failed: ${error.message}`);
        }
    }
}

// Optimized image processing with concurrency
async function processImages(theme) {
    const startTime = Date.now();
    const CONCURRENCY = 8;

    try {
        const { glob } = await import('glob');
        const imageFiles = glob.sync(`${theme}/app/images/**/*.{jpg,jpeg,png,webp}`, { nodir: true });

        async function processImage(file) {
            try {
                const outputPath = file.replace('/app/images/', '/dist/images/');
                const outputDir = path.dirname(outputPath);

                await fs.mkdir(outputDir, { recursive: true });

                const ext = path.extname(file).toLowerCase();
                if (ext === '.jpg' || ext === '.jpeg') {
                    await sharp(file)
                        .jpeg({ quality: 80, progressive: true })
                        .toFile(outputPath);
                } else if (ext === '.png') {
                    await sharp(file)
                        .png({ compressionLevel: 9, progressive: true })
                        .toFile(outputPath);
                } else if (ext === '.webp') {
                    await sharp(file)
                        .webp({ quality: 80 })
                        .toFile(outputPath);
                }

                log(theme, `📷 Optimized ${path.basename(file)}`);
            } catch (error) {
                log(theme, `❌ Image failed ${file}: ${error.message}`);
            }
        }

        // Process images with concurrency
        const queue = [...imageFiles];

        async function processNext() {
            while (queue.length) {
                const file = queue.pop();
                if (file) {
                    await processImage(file);
                }
            }
        }

        await Promise.all(Array(CONCURRENCY).fill(0).map(() => processNext()));

        // Copy SVG files
        const svgFiles = glob.sync(`${theme}/app/images/**/*.svg`, { nodir: true });
        for (const file of svgFiles) {
            const outputPath = file.replace('/app/images/', '/dist/images/');
            const outputDir = path.dirname(outputPath);
            await fs.mkdir(outputDir, { recursive: true });
            await fs.copyFile(file, outputPath);
        }

        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        log(theme, `✅ ${imageFiles.length + svgFiles.length} images processed in ${duration}s`);
    } catch (error) {
        log(theme, `❌ Image processing failed: ${error.message}`);
    }
}

// Process fonts
async function processFonts(theme) {
    try {
        const fontFolder = `${theme}/app/fonts`;
        const outputFolder = `${theme}/dist/fonts`;

        await fs.access(fontFolder);
        await fs.mkdir(outputFolder, { recursive: true });

        const { glob } = await import('glob');
        const fontFiles = glob.sync(`${fontFolder}/**/*.{eot,svg,ttf,woff,woff2}`, { nodir: true });

        for (const file of fontFiles) {
            const fileName = path.basename(file);
            await fs.copyFile(file, path.join(outputFolder, fileName));
        }

        log(theme, `✅ ${fontFiles.length} fonts processed`);
    } catch (error) {
        if (error.code === 'ENOENT') {
            log(theme, '📝 No font folder');
        } else {
            log(theme, `❌ Font processing failed: ${error.message}`);
        }
    }
}

// Build single theme
async function buildTheme(theme) {
    const startTime = Date.now();
    log(theme, '🚀 Starting build...');

    try {
        // Run all theme tasks in parallel
        await Promise.all([
            buildSCSS(theme),
            processImages(theme),
            processFonts(theme)
        ]);

        // Admin styles after main styles
        await buildAdminSCSS(theme);

        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        log(theme, `✅ Build completed in ${duration}s`);
    } catch (error) {
        log(theme, `❌ Build failed: ${error.message}`);
        throw error;
    }
}

// Multi-process theme building using worker processes
async function buildThemesParallel() {
    logGlobal('🔥 Building themes with true multi-process parallelism...');
    const startTime = Date.now();

    // Split themes into groups for optimal CPU utilization
    const numCPUs = 4; // Adjust based on your machine
    const themeGroups = [];
    const groupSize = Math.ceil(themes.length / numCPUs);

    for (let i = 0; i < themes.length; i += groupSize) {
        themeGroups.push(themes.slice(i, i + groupSize));
    }

    // Spawn separate Node processes for each group
    const processes = themeGroups.map((group, index) => {
        return new Promise((resolve, reject) => {
            const child = spawn('node', [
                'build.mjs',
                '--themes',
                group.join(','),
                ...(isDev ? ['--dev'] : [])
            ], {
                stdio: 'inherit'
            });

            child.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`Process ${index} failed with code ${code}`));
                }
            });
        });
    });

    await Promise.all(processes);

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    logGlobal(`✅ All themes built in ${duration}s`);
}

// Build themes in current process (faster for smaller sets)
async function buildThemesSequential() {
    logGlobal('⚡ Building themes sequentially...');
    const startTime = Date.now();

    // Process themes in parallel within this process
    await Promise.all(themes.map(theme => buildTheme(theme)));

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    logGlobal(`✅ All themes built in ${duration}s`);
}

// Main build function
async function main() {
    try {
        const startTime = Date.now();
        logGlobal('🚀 Starting esbuild-powered build...');

        // Handle specific theme groups (for multi-process)
        const themesArgIndex = process.argv.indexOf('--themes');
        if (themesArgIndex !== -1 && process.argv[themesArgIndex + 1]) {
            const selectedThemes = process.argv[themesArgIndex + 1].split(',');
            logGlobal(`Building specific themes: ${selectedThemes.join(', ')}`);
            await Promise.all(selectedThemes.map(theme => buildTheme(theme)));
            return;
        }

        // Clean first
        await cleanDist();

        // Build JavaScript (global, only once)
        await buildJavaScript();

        // Build themes
        if (isParallel) {
            await buildThemesParallel();
        } else {
            await buildThemesSequential();
        }

        const totalDuration = ((Date.now() - startTime) / 1000).toFixed(2);
        logGlobal(`🎉 Complete build finished in ${totalDuration}s`);

    } catch (error) {
        console.error('❌ Build failed:', error);
        process.exit(1);
    }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
