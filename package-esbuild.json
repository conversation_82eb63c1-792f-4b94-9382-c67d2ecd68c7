{"name": "phoenix-nodejs-esbuild", "version": "2.9.3", "description": "High-performance asset generation using esbuild + Parcel", "main": "build.mjs", "scripts": {"start": "node build.mjs --dev", "build": "node build.mjs", "build:fast": "node build.mjs --parallel", "dev": "node build.mjs --dev --watch"}, "author": "Phoenix Team - ComeOn!", "license": "ISC", "repository": {"type": "git", "url": "https://bitbucket.org/comeonwordpress/phoenix/", "directory": "/"}, "devDependencies": {"esbuild": "^0.20.0", "parcel": "^2.12.0", "@parcel/transformer-sass": "^2.12.0", "@parcel/optimizer-esbuild": "^2.12.0", "sharp": "^0.33.5", "chokidar": "^4.0.1", "node-fetch": "^3.3.2", "del": "^7.1.0", "concurrently": "^8.2.2"}}